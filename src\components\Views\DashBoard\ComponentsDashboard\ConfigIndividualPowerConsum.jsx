import React, { useContext, useEffect, useState } from 'react'
import { Button, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, Divider, FormControl, Grid, InputLabel, makeStyles, MenuItem, Select, TextField, Typography } from '@material-ui/core'
import { HighchartsReact } from 'highcharts-react-official';
import Highcharts from "highcharts/highstock";
import { KeyboardDatePicker, MuiPickersUtilsProvider } from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { setYear } from 'date-fns';
import { UserContext } from '../../../../context/UserProvider';
import { db, functions } from '../../../../config/firebase';


// Opciones de tiempo
const opcionesTiempo = [
  { value: 'mes', label: 'Mes' },
  { value: 'semana', label: 'Semana' },
  { value: 'año', label: 'Año' }
];

const opcionesMeses = [
  '<PERSON><PERSON>', 'Febrero', '<PERSON><PERSON>', '<PERSON>bri<PERSON>', '<PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON>gosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

function getYearOptions() {
  const years = [];
  const currentYear = new Date().getFullYear();
  for (let i = 1; i < 3; i++) {
    years.push(currentYear - i);
  }
  return years;
}

const useStyles = makeStyles((theme) => ({
  redHeader: {
    // fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,

  },
  boxSection: {
    border: '1.5px dashed #ffffffff',
    borderRadius: 10,
    marginBottom: 18,
    padding: '18px 12px',
    background: '#ffffffff',
  },
  // dividerDashed: {
  //   borderTop: '1.5px dashed #000000ff',
  //   margin: '22px 0',
  // },
  centeredButton: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 14,
    marginBottom: 10,
  },
  codeFont: {
    fontFamily: "'Fira Mono','Consolas', monospace",
    fontSize: '1.1em',
  },
  graficaArea: {
    border: '1.5px dashed #ffffffff',
    borderRadius: 10,
    minHeight: 180,
    marginTop: 12,
    background: '#ffffffff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

export const ConfigIndividualPowerConsum = ({open,togglesNames,togglesIds,volt,onChangeVolt,onClose}) => {
  const classes = useStyles();
  const { usuario, currentMac, canIdIrrigation } = useContext(UserContext);
  const [voltage, setVoltage] = useState(volt)
	const [tiempo, setTiempo] = useState('mes');
	const [mesSeleccionado, setMesSeleccionado] = useState('Junio');
  const [selectedDevices, setSelectedDevices] = useState([])
  const [graphicDevices, setGraphicDevices] = useState([])
	const [datos, setDatos] = useState(null);
	const [cargando, setCargando] = useState(false);
  const [startDate, setStartDate] = useState(new Date() - 7 * 24 * 60 * 60 * 1000);
  const [endDate, setEndDate] = useState(new Date());
  const [yearOptions, setYearOptions] = useState([]);
  const [yearSelected, setYearSelected] = useState(new Date().getFullYear() - 1);

  
  const getDataFromBQ = async (uid, fIni, fFin) => {
    let valArray = [];
    const getDataSetValues = functions.httpsCallable("getDataSetValues");
    const values = await getDataSetValues({
      user: usuario.username,
      uid: uid.trim(),
      dateStart: fIni,
      dateFinish: fFin,
    });

    let data = values.data.result[0];
    for (let i in data) {
      let newEntry = [Date.parse(data[i].timestamp.value), data[i].val];
      valArray.push(newEntry);
    }
    
    //console.log(valArray)
    return valArray;
  };


	// Simulación de obtención de datos (aquí iría tu consulta a BigQuery)
  const obtenerDatos = async () => {
    setCargando(true);
    // Simular espera
    await new Promise(res => setTimeout(res, 1500));
    // Simular datos para la gráfica
    setDatos({
      categories: ['1', '2', '3', '4', '5', '6', '7'],
      series: selectedDevices.map((id, idx) => ({
        name: togglesNames[idx],
        data: Array.from({ length: 7 }, () => Math.floor(Math.random() * 230 + 20))
      }))
    });
    setCargando(false);
  };

  useEffect(() => {
    if (selectedDevices.length > 0) {
      obtenerDatos();
    }
  }, [tiempo, mesSeleccionado, selectedDevices]);

  useEffect(() => {
    const arrayOfYears = getYearOptions();
    if(arrayOfYears.length) {
      setYearOptions (arrayOfYears);
    }
  }, [])
  

  const handleVoltageChange = (e) => {
    if(e.target.value >= 0) {
      setVoltage(Number(e.target.value));
    } else {
      setVoltage(0);
    }
    
  }

  const saveVoltage = async () => {
    if (!usuario.username || currentMac === ""  || canIdIrrigation === "") return;
    try {
      const path = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/configModule`;
      const docRef = db.collection(path).doc("powerConsum");
      await docRef.set({
        voltage: voltage
      }, { merge: true });
      onChangeVolt(voltage);
    } catch (error) {
      console.error("Error en el guardado del voltaje:",error)
      alert("Error en el guardado del voltaje")
    }
  }

  const handleStartDateChange = (date) => {
    setStartDate(date);
    const startDateString = new Date(date._d.getTime()).toISOString();

  };

  const handleEndDateChange = (date) => {
    setEndDate(date);
  };

  // Configuración de Highcharts
  const chartOptions = {
    title: { text: 'Corriente histórico' },
    xAxis: {
      categories: datos?.categories || []
    },
    yAxis: {
      title: { text: 'Corriente (A)' }
    },
    series: datos?.series || []
  };

  // useEffect(() => {
  //   if(selectedDevices.length > 0) {
  //     // Buscar ids agregados
  //     const added = selectedDevices.filter(id => !graphicDevices.includes(id));
  //     // Buscar ids eliminados
  //     const removed = selectedDevices.filter(id => !graphicDevices.includes(id));
  //     if (added.length > 0) {
        
  //     } else if (removed.length > 0) {
        
  //     }
  //   }
  // }, [selectedDevices,graphicDevices])
  


  return (
     <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogContent>
        {/* --- Sección Editar Voltaje --- */}
        <div className={classes.boxSection}>
          <Typography variant="h5" className={classes.redHeader}>Voltaje</Typography>
          <Grid container spacing={2} alignItems="center" justifyContent="center">
            <Grid item xs={12} md={7} style={{ textAlign: 'center', justifyContent: 'center', display: 'flex' }}>
              {/* <span className={classes.codeFont}>[</span> */}
              <TextField
                variant="outlined"
                type="number"
                value={voltage}
                onChange={handleVoltageChange}
                // inputProps={{ style: { textAlign: 'center', width: 70} }}
                style={{ margin: '0 10px' }}
                size='small'
              />
              <span>V</span>
            </Grid>
            <Grid item xs={12} className={classes.centeredButton}>
              <Button variant="contained" color="primary" style={{ minWidth: 180 }} onClick={saveVoltage}>
                Editar Voltaje
              </Button>
            </Grid>
          </Grid>
        </div>

        <Divider />

        {/* --- Sección Visualización de Gráficas --- */}
        <div className={classes.boxSection}>
          <Typography variant="h5" className={classes.redHeader}>Histórico de Consumo Eléctrico</Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              {/* <Typography className={classes.codeFont}>
                <span>Consultar por:</span> {tiempo}
              </Typography> */}
              <FormControl variant="outlined" fullWidth margin="dense">
                <InputLabel>Consultar por</InputLabel>
                <Select
                  value={tiempo}
                  onChange={e => setTiempo(e.target.value)}
                  label="Consultar por"
                >
                  {opcionesTiempo.map(op => (
                    <MenuItem key={op.value} value={op.value}>{op.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {tiempo === 'mes' && (
              <Grid item xs={12} md={4}>
                <FormControl variant="outlined" fullWidth margin="dense">
                  <InputLabel>Mes</InputLabel>
                  <Select
                    value={mesSeleccionado}
                    onChange={e => setMesSeleccionado(e.target.value)}
                    label="Mes"
                  >
                    {opcionesMeses.map(mes => (
                      <MenuItem key={mes} value={mes}>{mes}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {tiempo === 'año' && (
              <Grid item xs={12} md={4}>
                <FormControl variant="outlined" fullWidth margin="dense">
                  <InputLabel>Año</InputLabel>
                  <Select
                    value={yearSelected}
                    onChange={e => setYearSelected(e.target.value)}
                    label="Año"
                  >
                    {yearOptions?.map((year,index) => (
                      <MenuItem key={index} value={year}>{year}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            
            {tiempo === 'semana' && (
              <Grid item xs={12} md={8}>
                <MuiPickersUtilsProvider utils={DateFnsUtils}>
                  <Grid 
                    container 
                    direction="row" // Horizontal
                    justifyContent="flex-start" // Alinea a la izquierda
                    alignItems="center" // Alinea verticalmente al centro
                    spacing={2} // Espacio entre pickers
                  >
                    <Grid item>
                      <KeyboardDatePicker
                        disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        margin="normal"
                        id="date-picker-inline"
                        label="Desde"
                        value={startDate}
                        onChange={handleStartDateChange}
                        KeyboardButtonProps={{
                          'aria-label': 'change date',
                        }}
                      />
                    </Grid>
                    <Grid item>
                      <KeyboardDatePicker
                        disableToolbar
                        variant="inline"
                        format="dd/MM/yyyy"
                        margin="normal"
                        id="date-picker-inline2"
                        label="Hasta"
                        value={endDate}
                        onChange={handleEndDateChange}
                        KeyboardButtonProps={{
                          'aria-label': 'change date',
                        }}
                      />
                    </Grid>
                  </Grid>
                </MuiPickersUtilsProvider>
              </Grid>
            )}

            <Grid item xs={12} md={12}>
              <FormControl variant="outlined" fullWidth margin="dense">
                <InputLabel>Dispositivos</InputLabel>
                <Select
                  multiple
                  value={selectedDevices}
                  onChange={e => setSelectedDevices(e.target.value)}
                  label="Dispositivos"
                  renderValue={selected =>
                    selected.map(index => togglesNames[index]).join(',')
                  }
                >
                  {togglesNames.length === 0 && (
                    <MenuItem value="" disabled>
                      NO hay dispositivos para mostrar
                    </MenuItem>
                  )}
                  {togglesNames?.map((toggle,index) => (
                    <MenuItem key={index} value={index}>
                      {toggle}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} className={classes.centeredButton}>
              <Button variant="contained" color="primary" style={{ minWidth: 180 }}>
                MOSTRAR GRÁFICA
              </Button>
            </Grid>
            <Grid item xs={12}>
              <div className={classes.graficaArea}>
                {cargando ? (
                  <CircularProgress />
                ) : (
                  datos && <HighchartsReact highcharts={Highcharts} options={chartOptions} />
                )}
                {!cargando && !datos && (
                  <Typography className={classes.codeFont}>
                    Área para la gráfica
                  </Typography>
                )}
              </div>
            </Grid>
          </Grid>
        </div>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary" variant="outlined">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>

	//  <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
  //     <DialogTitle>Editar Voltaje y Visualizar Datos</DialogTitle>
  //     <DialogContent>
  //       <Grid container spacing={3}>
  //         <Grid item xs={12} sm={4}>
  //           <Typography variant="subtitle1">Editar Voltaje</Typography>
  //           <TextField
  //             variant="outlined"
  //             margin="normal"
  //             type="number"
  //             label="Voltaje"
  //             value={voltaje}
  //             onChange={handleVoltajeChange}
  //             fullWidth
  //             inputProps={{ min: 0 }}
  //           />
  //         </Grid>

  //         <Grid item xs={12} sm={8}>
  //           <Grid container spacing={2}>
  //             <Grid item xs={6} sm={4}>
  //               <FormControl variant="outlined" fullWidth>
  //                 <InputLabel>Tiempo</InputLabel>
  //                 <Select
  //                   value={tiempo}
  //                   onChange={e => setTiempo(e.target.value)}
  //                   label="Tiempo"
  //                 >
  //                   {opcionesTiempo.map(op => (
  //                     <MenuItem key={op.value} value={op.value}>{op.label}</MenuItem>
  //                   ))}
  //                 </Select>
  //               </FormControl>
  //             </Grid>
  //             {tiempo === 'mes' && (
  //               <Grid item xs={6} sm={4}>
  //                 <FormControl variant="outlined" fullWidth>
  //                   <InputLabel>Mes</InputLabel>
  //                   <Select
  //                     value={mesSeleccionado}
  //                     onChange={e => setMesSeleccionado(e.target.value)}
  //                     label="Mes"
  //                   >
  //                     {opcionesMeses.map(mes => (
  //                       <MenuItem key={mes} value={mes}>{mes}</MenuItem>
  //                     ))}
  //                   </Select>
  //                 </FormControl>
  //               </Grid>
  //             )}
  //             {/* Puedes agregar aquí selectores de semana o año según tu necesidad */}
  //             <Grid item xs={12} sm={4}>
  //               <FormControl variant="outlined" fullWidth>
  //                 <InputLabel>Dispositivos</InputLabel>
  //                 <Select
  //                   multiple
  //                   value={dispositivosSeleccionados}
  //                   onChange={e => setDispositivosSeleccionados(e.target.value)}
  //                   label="Dispositivos"
  //                   renderValue={selected =>
  //                     selected.map(id => listaDispositivos.find(d => d.id === id)?.nombre).join(', ')
  //                   }
  //                 >
  //                   {listaDispositivos.map(d => (
  //                     <MenuItem key={d.id} value={d.id}>
  //                       {d.nombre}
  //                     </MenuItem>
  //                   ))}
  //                 </Select>
  //               </FormControl>
  //             </Grid>
  //           </Grid>
  //         </Grid>
  //         <Grid item xs={12}>
  //           <div style={{ minHeight: 300, position: 'relative' }}>
  //             {cargando ? (
  //               <div style={{
  //                 display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300
  //               }}>
  //                 <CircularProgress />
  //               </div>
  //             ) : (
  //               datos && <HighchartsReact highcharts={Highcharts} options={chartOptions} />
  //             )}
  //           </div>
  //         </Grid>
  //       </Grid>
  //     </DialogContent>
  //     <DialogActions>
  //       <Button onClick={onClose} color="primary" variant="outlined">
  //         Cerrar
  //       </Button>
  //       <Button onClick={() => alert(`Voltaje actualizado a ${voltaje} V`)} color="primary" variant="contained">
  //         Guardar Voltaje
  //       </Button>
  //     </DialogActions>
  //   </Dialog>
  )
}
